[{"api_id": "kuCwZZZTxJJlyMG0nNH6L5eIPUW", "endpoint": "/"}, {"api_id": "hK0Damt_YYqgqapxjCDS7IlMEK0", "endpoint": "/lib/jquery-1.9.1.js"}, {"api_id": "ubzj1GlUxoeigWVZ2fuoVF7WbBE", "endpoint": "/HTTP/functionality/HTTP-3-7/{id1}"}, {"api_id": "jtqh0HHE40CPeQ.jpOiQs33DJs9", "endpoint": "/Misc/{id1}"}, {"api_id": "v8G1Jx9UZpiwvyxtXAGzl8gFclg", "endpoint": "/Misc/fpTest"}, {"api_id": "lYJeZozLfm5yeD.In05FVJcRO7l", "endpoint": "/Misc/submit"}, {"api_id": "h521R1WSXbvUePt2d0GbQY8OIHg", "endpoint": "/lib/bianStyle.css"}, {"api_id": "2mu17D4nBVUVQJ..q_oDW6MhHtq", "endpoint": "/lib/bootstrap.min.css"}, {"api_id": "mlWZfWYA0LiT06YnOajU3vehz83", "endpoint": "/lib/jquery.cookie-1.4.1.js"}, {"api_id": "5r9ii7zkMn9x.iuosIZwpdVKfRg", "endpoint": "/lib/bootstrap.js"}, {"api_id": "7iVuWu7FNKO3Ut9lqdT_zocWWgE", "endpoint": "/asset/logo_bi-an.png"}, {"api_id": "oAoRC8D3qjjsK6ItdN7QQmgYs5W", "endpoint": "/static/dictionary.php"}, {"api_id": "lOtvC0VFxuNaJD85Tn2WIw2arW3", "endpoint": "/1k.html"}, {"api_id": "Xlhmr9Kwj6YwLBcdpV.Hu94XumV", "endpoint": "/2k.html"}, {"api_id": "ygNrGbFuV1ucCzxG0rtZUKp6ciV", "endpoint": "/Mix_12.html"}, {"api_id": "cLszqCT6UJLtSEkY9.b6LWOBp9Z", "endpoint": "/performance/complex_dynamic"}, {"api_id": "mjEJToXgfxVsIL7p6_yoBDIAPWL", "endpoint": "/NGJS/tSuite/2"}, {"api_id": "qfHKbo_VfPj1FhGeWTXLFc5tDIG", "endpoint": "/view/REVOL/job/revol/job/REVOL-794/lastSuccessfulBuild/artifact/deployer-REVOL-794-8678a7-x86_64.AppImage/test01/test/123/fsrw-test/324-pwd/test/test/ignore-path/{id1}/{id2}"}, {"api_id": "rp4iebSPHivUQC5UMOaTaMCVIaa", "endpoint": "/lib/angular.js"}, {"api_id": "PVyNra8LkcDekH9qJprK4j.VomA", "endpoint": "/HTTP/cache-o/{id1}"}, {"api_id": "B4lQyBiEwj2VYGo0Mc7Rao8rGE7", "endpoint": "/NGJS/tSuite/{id1}"}, {"api_id": "HtDLwAShrvMUqaD6kPA5Kn9FFyE", "endpoint": "/compatibility/results/"}, {"api_id": "CB3Jqf.awxTgB3MW7vs6rH3eVgW", "endpoint": "/performance/static"}, {"api_id": "arZvfxr87yK2iJXzgBe8q0JdqP0", "endpoint": "/NGJS/tSuite-2-formHelper.js"}, {"api_id": "ezP_vuSY8i6ilSH2G4eAgwxsLYV", "endpoint": "/NGJS/tSuite-2-form.js"}, {"api_id": "ZKagAyJVHcmbuWG4kFrcLOLQ_e0", "endpoint": "/HTTP/functionality/HTTP-3-7/{id1}"}, {"api_id": "UFLz77skf1kZ9M_UZ0tCSP4rodE", "endpoint": "/HTTP/functionality/HTTP-3-7/{id1}"}, {"api_id": "e59GO19IXG7dPSItlyiPW6zVcfA", "endpoint": "/compatibility/urlencode/"}, {"api_id": "huatYjwsEz1v0_M8sn89WxEn8Wq", "endpoint": "/HTTP/cache"}, {"api_id": "_43zaKuCeNaq.hnV9NiMV7LciPE", "endpoint": "/Vue/ut-index"}, {"api_id": "ZkgKvJXdxz_drO2.f96jnyesJeq", "endpoint": "/compatibility/results/"}, {"api_id": "kTS3Nrz9D45B.43fcdTvNU8Xth9", "endpoint": "/HTTP/tSuite-4-cache-o.js"}, {"api_id": "SrZMr3Rls6mN2Qj19b_uorVmT03", "endpoint": "/url/url"}, {"api_id": "4ZTmGlbWNDxta3gARYcSzZjADZL", "endpoint": "/url/base_static_relative/"}, {"api_id": "RiR9JWjHI4XlJCszSC1iaiYGzRE", "endpoint": "/training/{id1}"}, {"api_id": "3yPfm2Ad4rNNf6Hy5sS4.2oO939", "endpoint": "/url/anchor/"}, {"api_id": "GaJHD24cWtf31r9RKYAme7leYUq", "endpoint": "/html_recognition_new/content_type/overwrite"}, {"api_id": "wJ0ArRAGlokI0J_YFjpJA0XgQA0", "endpoint": "/HTTP/functionality/HTTP-3-8"}, {"api_id": "es8SkrkLFIJ_6XmIzbIf2vr7u59", "endpoint": "/HTTP/functionality/HTTP-3-6"}, {"api_id": "RKP9d9DxzYoiDmmBB8syykrcsZZ", "endpoint": "/HTTP/methods"}, {"api_id": "QsMt8e.WwiNPGMplniWrhjpL427", "endpoint": "/HTTP/tSuite-2-cache.js"}, {"api_id": "ZoFJH9urTc2nOPCwZsdBMt.wudA", "endpoint": "/vue/echo/2"}, {"api_id": "g5sxjZmXQiY27RY0EyjtFmVT4ea", "endpoint": "/HTTP/methods"}, {"api_id": "3dCIzFUbN4Wf4.DAGJzrDjKOltG", "endpoint": "/html_recognition_new/txsafe_html_strict_mode/"}, {"api_id": "AvDw_OMgY15wDRhfp70dHsmZgma", "endpoint": "/html_recognition_new/accept_ranges"}, {"api_id": "IYwouyBAJWLv1xl9I09ngwL0Trg", "endpoint": "/html_recognition_new/txsafe_html_strict_mode"}, {"api_id": "5cqs1jO4uigIcamNkiQCe5XPZ9G", "endpoint": "/HTTP/functionality/HTTP-3-6/redirect"}, {"api_id": "ijE_5ac_W8r1cQOx6UYw0Awp3ll", "endpoint": "/url/base_dynamic_relative/"}, {"api_id": "VCBfZQpOgAeFB99FJJlrNEU1t3l", "endpoint": "/waf_test_tool/sensitive_info/sensitive_info_test.html"}, {"api_id": "e4Us2Piow2oZqmvSAFJ6daD.gOL", "endpoint": "/compatibility/anchor/"}, {"api_id": "ntfnW_._TJqUTJ1l0wnox4tM_9G", "endpoint": "/api/login/refresh_token/backend/"}, {"api_id": "jUIkTorJrnm0en.0sjOlbAirI1Q", "endpoint": "/static/static.html"}, {"api_id": "1ocsbO1ffwcFRM.g_VcrH18U1hV", "endpoint": "/HTTP/tSuite-3-functionality.js"}, {"api_id": "Hxlmd9lCKi58QKiMvHqz.B07Q39", "endpoint": "/react/api/comments"}, {"api_id": "liCgRPVZUeSNo39klzz1wz8OZfE", "endpoint": "/vue/echo/2"}, {"api_id": "RBk.aj_cV895Zlc.87Ibj0fvSKQ", "endpoint": "/NGJS/tSuite/cba"}, {"api_id": "Wp39X91LRXv.ZJtgKzA77zbEbSV", "endpoint": "/"}, {"api_id": "6i9lUGR2lNbI6KLaeY0uy_pCYqg", "endpoint": "/url/test_red/"}, {"api_id": "ddk8nVrF6jZ8c0cnGIgaZqMsGeq", "endpoint": "/url/a/onclick/"}, {"api_id": "vS9j1LzGBEK7KCji_y5km76hZ6Z", "endpoint": "/intercept/location/"}, {"api_id": "pJyuQUite6huy4FqenJ6n1uLhoW", "endpoint": "/html_recognition_new/content_type"}, {"api_id": "uA2Qnuxck65N8TTtsmVVz8GwO53", "endpoint": "/compatibility/urlencode/gb2312/"}, {"api_id": "WWYr3FUfN2l8PSyyzwg2Z5nC_QL", "endpoint": "/compatibility/urlencode/normal/:@-._~!$&'()*+,=;:@-._~!$&'()*+,=:@-._~!$&'()*+,=="}, {"api_id": "8hRiUzPW.5qZpKKB0bzcvKVUvl3", "endpoint": "/favicon.ico"}, {"api_id": "Ri9baTzpGIq1D7kL9zAzPSejfN3", "endpoint": "/lib/jasmine-core/boot.js"}, {"api_id": "lKeP1UQz2thB5IXqI5rILbsituE", "endpoint": "/lib/jasmine-core/jasmine-html.js"}, {"api_id": ".BYUYyTXpIg.eNlkT7t2O4Jx_1A", "endpoint": "/lib/jasmine-core/json2.js"}, {"api_id": "RAKgO84UhqxqGECvKxAEgL.JFVg", "endpoint": "/NGJS/tSuite-1-url.js"}, {"api_id": "R7Aw9k8pyyeDHbS6P5ZFMCElkrA", "endpoint": "/lib/jasmine-core/jasmine.css"}, {"api_id": "YXYNMnWq4F8PP7u7Gyd_LbVQaHl", "endpoint": "/partials/nav.html"}, {"api_id": "t0gheBmu_X1NhV8cn2Ju5coW1p3", "endpoint": "/lib/jasmine-core/jasmine.js"}, {"api_id": "h0awstx1lL01jOsma7kKw224sfg", "endpoint": "/lib/vue.js"}, {"api_id": "LXaNE53DoQ73StHMixkpN6f67Wg", "endpoint": "/static/scripts/script_obfuscation.static"}, {"api_id": "PmoOnUB4CBIAiTPNm4EYqMeq1hl", "endpoint": "/html_recognition_new/media_files/"}, {"api_id": "xkQHKTiL2blMcajDY0zrTdTvHWg", "endpoint": "/redirect/ajax/transition/"}, {"api_id": "fdiWZBJhrMBp_7_jhDd17QnAFiV", "endpoint": "/html_recognition_new/non_ajax/"}, {"api_id": "GxNLOfp2Gts3DYMlfd7n4LYb5Nl", "endpoint": "/login/"}, {"api_id": "04cpFb0Tu6t_XlFwylwepDbAf8g", "endpoint": "/compatibility/form/"}, {"api_id": "yic4fYV8WP6s2lKUrGA8IzehUUl", "endpoint": "/script_obfuscation/"}, {"api_id": "OpDGxfBfbgO.WpxSO4ZthsP6xGV", "endpoint": "/cookie_handling/"}, {"api_id": "u8dpxeiZqqujiCqLwaeXraPZikV", "endpoint": "/api/login/token_in_set_cookie/backend/"}, {"api_id": "Pm.Z2yqk_2lkbc5UkPrU4loywQZ", "endpoint": "/redirect/ajax/transition/"}, {"api_id": "m7MWqRcqYsi57SzY.Vzqz4Gl.al", "endpoint": "/html5/form/"}, {"api_id": "OsRBu1pmn15S2_ZXfTMmlL3eHxW", "endpoint": "/cookie/"}, {"api_id": "oy00aNIA1G2VozGhDtfr3elhseg", "endpoint": "/gb2312.js"}, {"api_id": "gd3AfaeelyL4G30zAjbR9rt4VJZ", "endpoint": "/api/login/token_in_authorization/backend/"}, {"api_id": "u3l6cE3KQ10Usi.qMjbMUpOAt6g", "endpoint": "/charset/"}, {"api_id": "oqzxEc1lPbQaT1KSY00Sql6jC0Q", "endpoint": "/compatibility/DOCTYPE/"}, {"api_id": "1SrIvoAxOFqLhfzBwP831AxB8T7", "endpoint": "/waf_test_tool/response/"}, {"api_id": "RMe5Gelbykd57oGBxXpPa.5sXg3", "endpoint": "/form/search/"}, {"api_id": "tzaHn4hS6mP2HvOQ3fS7hSf6uo0", "endpoint": "/compatibility/urlencode/ajax/:@-._~!$&'()*+,=;:@-._~!$&'()*+,=:@-._~!$&'()*+,=="}, {"api_id": "ceK4rsgLuyJRnsJAsQd379nf__V", "endpoint": "/compatibility/urlencode/ajax/:@-._~!$&'()*+,=;:@-._~!$&'()*+,=:@-._~!$&'()*+,=="}, {"api_id": "Ri4mgj31nJMRZQsWSvKFQF1TiSG", "endpoint": "/form/full_tests/"}, {"api_id": "A7j394iEmd52DimIHAUhnpJsaN9", "endpoint": "/compatibility/urlencode/normal/路径;路径参数名=路径参数值"}, {"api_id": "H4kmzyB1uWvnBCGG99KAB_O02Vl", "endpoint": "/custom_content"}, {"api_id": "_VHPN7PmCk2KJS84aOrXIp2TX5q", "endpoint": "/form/add/"}, {"api_id": "5gEGpCK.VWEwdZDx.DG84tFSobV", "endpoint": "/form/search/"}, {"api_id": "TJEJxcS8EsFHaAevcErgbYl8UN7", "endpoint": "/html_recognition_new/no_content_type_"}, {"api_id": "DPqNNY_sV3nzHeQKou_9aYENSbq", "endpoint": "/JS/Ajax/JS-2-1"}, {"api_id": "qxbzSjYJNqVOeRijJWDAsXgcWJW", "endpoint": "/HTTP/cache-o/http-4-1/fonts/glyphicons-halflings-regular.woff2"}]