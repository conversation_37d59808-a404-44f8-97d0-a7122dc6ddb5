# -*- encoding: utf-8 -*-
import requests
import json
import re
import time
import random
from api_name_reasoning.utils import read_json, get_mock_data_file


def gen_clickhouse_sql(mock_data):
    ids = ["'{}'".format(api['id']) for api in mock_data]

    sql = """
    select 
        api_id_sample AS id,
        any(api_snap_reqheader) AS req_header,
        any(api_snap_respbody) AS resp_body,
        any(api_snap_respheader) AS resp_header
    from 
        first_api_sample 
    where 
        api_id_sample in [{ids}]
    group by api_id_sample
    """.format(ids=','.join(ids))

    print(sql)

def gen_api_sample_json_file(mock_data, filename="api_samples.json", is_sample=True):
    if is_sample:
        keys = ["api_id", "req_header", "resp_body", "resp_header"]
        id_mapping = {api['id']: api['endpoint'] for api in mock_data}
        api_samples = read_json("first_api_sample.json")
        samples = []
        for sample in api_samples:
            tmp = dict(zip(keys, sample))
            tmp['endpoint'] = id_mapping.get(tmp['api_id'], '')
            samples.append(tmp)
    else:
        samples = [{'api_id':api['id'], 'endpoint':api['endpoint']} for api in mock_data]

    with open(get_mock_data_file(filename), "w") as f:
        json.dump(samples, f, ensure_ascii=False, indent=4)

if __name__ == '__main__':
    mock_data = read_json("api_info.json")
    gen_clickhouse_sql(mock_data)
    gen_api_sample_json_file(mock_data)
    gen_api_sample_json_file(mock_data, filename='api_no_samples.json', is_sample=False)
