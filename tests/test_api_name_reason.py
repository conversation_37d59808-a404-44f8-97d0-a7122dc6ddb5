# -*- encoding: utf-8 -*-
import time
import random
from ai_reasoning.utils import read_json
from ai_reasoning.llm import *

def test_runner(mock_data, count=10):
    api_info_index = set()
    api_infos = []
    # print(" random api ".center(80, '-'))
    while True:
        random_number = random.randint(0, 99)
        api_info_index.add(random_number)
        if len(api_info_index) >= count:
            break
    # print(" get apis ".center(80, '-'))
    for index in api_info_index:
        api = mock_data[index]
        api_infos.append(
            {
                "api_id": api['id'],
                "endpoint": api['endpoint']
            }
        )
    start_time = time.time()

    if api_infos:
        try:
            # print(" reasoning api ".center(80, '-'))
            api_name_reasoning(api_infos=api_infos)
        except Exception as e:
            print(" reasoning api error {}".format(e).center(80, '-'))
            pass
        cost_time = time.time() - start_time
        # time.sleep(random.randint(1, 5))
        return cost_time

def test_api_no_sample(count=10, runs=50):
    print('{} APIs no sample, {} times'.format(count, runs).center(80, '-'))
    cost_times = []
    mock_data = read_json("api_no_samples.json")
    for _ in range(0, runs):
        print('run {}'.format(_).center(80, '-'))
        cost_times.append(test_runner(mock_data, count=count))

    print('cost: {}s, max: {}s, min: {}s, total: {}s'.format(sum(cost_times) / len(cost_times), max(cost_times), min(cost_times), sum(cost_times)))
    
def test_api_sample(count=10, runs=50):
    print('{} APIs no sample, {} times'.format(count, runs).center(80, '-'))
    cost_times = []
    mock_data = read_json("api_samples.json")
    for _ in range(0, runs):
        print('run {}'.format(_).center(80, '-'))
        cost_times.append(test_runner(mock_data, count=count))

    print('cost: {}s, max: {}s, min: {}s, total: {}s'.format(sum(cost_times) / len(cost_times), max(cost_times), min(cost_times), sum(cost_times)))
    
def test_rand_api_name_reason():
    mock_data = read_json("api_no_samples.json")
    now = time.time()
    print('start time: {}'.format(now).center(80, '-'))
    random_number = random.randint(0, 99)
    api = mock_data[random_number]
    print(api)
    content = api_name_reasoning(api_infos=[
        {
            "api_id": api['api_id'],
            "endpoint": api['endpoint']
        }
        ])
    print(content)
    print('cost: {}s'.format(time.time() - now).center(80, '-'))
    

if __name__ == '__main__':
    test_rand_api_name_reason()

    # test_api_no_sample(count=1, runs=50)
    # test_api_no_sample(count=10, runs=5)

    # test_api_no_sample(count=1, runs=500)
    # test_api_no_sample(count=10, runs=50)

    # test_api_no_sample(count=1, runs=1000)
    # test_api_no_sample(count=10, runs=100)

    # test_api_no_sample(count=1, runs=10000)
    # test_api_no_sample(count=10, runs=1000)

    
    # assistant_messages = [{
    #     'role': 'assistant',
    #     'content': content, 
    # },
    # {
    #     'role': 'assistant',
    #     'content': COMPACT_PROMPT
    # }]
    # messages = get_system_messages()
    # messages.extend(assistant_messages)
    # compact_data = send_message(messages)
    # print(compact_data)
