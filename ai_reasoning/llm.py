# -*- encoding: utf-8 -*-
import requests
import re
import json
import os
from api_name_reasoning.utils import read_json
from dotenv import load_dotenv

load_dotenv()

MODEL_URL = os.getenv('MODEL_URL', 'https://openrouter.ai/api/v1/chat/completions')
MODEL_API_KEY = os.getenv('MODEL_API_KEY', '')
MODEL_NAME = os.getenv('MODEL_NAME', 'qwen/qwen3-4b:free')

SYSTEM_PROMPT = """
你现在是一个专业API名称推理助手，帮助用户推理生成符合业务含义的API名称（中文名称），名称必须符合以下要求：  

1. 中文名称长度限制在15个汉字字符以内，允许中英文名称混合。
2. 名称需要符合API的业务含义，不能是无意义的名称，比如不能将`/v1/ai_text_gen`生成`v1_text`这样无意义的名称。
3. 当没有合适的名称时，使用空字符串(`""`)表示。
4. 当用户提供的数据中存在SQL注入，XSS攻击等时。需要避免使用相关攻击事件名称作为API的名称， 例如：不允许使用SQL注入，XSS注入等。
5. 不允许使用URL路径作为名称。
6. 推理API名称出现重复时，需要加上编号。

重要提示：当用户提供的数据中包含HTTP的详细请求和响应上下文时，优先采用以下分析逻辑，然后生成名称，生成的名称必须如何相关要求。

1. 如果用户提供的数据中包含请求路径，请求头，请求体和响应头，响应体等上下文信息时，分析API的具体行为，并生成名称。
2. 如果用户提供的数据中响应状态码大于等于400，表示请求失败，跳过该分析。
3. 如果用户提供的数据中缺失响应体时，仅分析请求路径，请求头，请求体，分析API大致行为，并生成名称。
4. 如果用户提供的数据中仅URL路径时，可根据请求路径关键字进行分析，并生成名称。
5. 如果用户提供的数据中URL路径相同，请求参数（包含query和body中的参数）动词行为不同时，则表示是不同的API，需要根据参数分析，并生成对应的名称， 例如`/user?action=create`表示创建用户， `/user?action=login` 表示用户登录。

# Tone and style

您应该简洁、直接、切中要点。
您必须用少于 4 行的内容简洁地回答（不包括工具使用或代码生成），除非用户要求详细信息。
重要提示：在保证实用性、质量和准确性的前提下，应尽可能减少输出标记。
重要提示：除非用户要求，否则您不应该使用不必要的前言或后言（例如解释您的结果或总结操作）来回答。除非用户要求，否则不要添加额外的解释。处理完后直接停止，而不是提供你所做的解释。

输出为json结构化数据格式,示例：

```json
[
    {"api_id":"xxx", "name":"API中文名称"}
    // ...
]
```
"""

USER_PROMPT = """
请分析以下API的api_id和endpoint数据，分别为他们生成对应的名称。
重要提示：当出现api_id和endpoint完全一致时，需要将重复的api_id和endpoint合并为一个。

{api_infos}
"""

COMPACT_PROMPT = """你是一个乐于助人的人工智能助手，负责总结对话。"""

ai_model_config = {
    'model_name': MODEL_NAME,
    'api_key': MODEL_API_KEY,
    'url': MODEL_URL
}

def extract_json_from_markdown(md_content):
    pattern = r'```json\s*([\s\S]*?)\s*```'
    match = re.search(pattern, md_content)
    if match:
        json_str = match.group(1).strip()
        return json.loads(json_str)
    else:
        raise ValueError("No JSON code block found")

def get_system_messages():
    return [
        {
            "role": "system",
            "content": SYSTEM_PROMPT
        }
    ]

def send_message(messages):
    response = requests.post(
        url=ai_model_config.get('url', ''),
        headers={
            "Authorization": "Bearer {api_key}".format(api_key=ai_model_config.get('api_key', '')),
            "Content-Type": "application/json",
        },
        data=json.dumps({
            "model": ai_model_config.get('model_name', ''),
            "messages": messages,
        }),
        verify=False
    )
    if response.status_code >= 400:
        raise Exception(response.text)
    
    return response.json()

def api_name_reasoning(api_infos):

    messages = get_system_messages()
    messages.append({
        "role": "user",
        "content": USER_PROMPT.format(api_infos=api_infos)
    })

    data = send_message(messages)
    choices = data.get('choices', [])
    if choices:
        content = choices[0].get('message', {}).get('content', '')
        return extract_json_from_markdown(content)
    return []

